# Maestro IDE - Main Entry Point
# نقطة الدخول الرئيسية لبيئة التطوير المتكاملة

load "src/app.ring"

func main
    /*
    الدالة: main
    الوصف: نقطة الدخول الرئيسية للتطبيق
    المدخلات: لا يوجد
    المخرجات: لا يوجد
    */
    
    try {
        # إنشاء مثيل من التطبيق الرئيسي
        oApp = new RingIDE()
        
        # بدء تشغيل التطبيق
        oApp.start()
        
    catch
        ? "خطأ في بدء التطبيق: " + cCatchError
        ? "تأكد من وجود جميع الملفات المطلوبة"
    }

# استدعاء الدالة الرئيسية
main()
